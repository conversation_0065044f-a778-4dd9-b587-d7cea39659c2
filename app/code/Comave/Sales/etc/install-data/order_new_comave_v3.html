{{template config_path="design/email/header_template"}}

<p>
    {{trans "****This is an automatically generated email, please do not reply****"}}
</p>
<div style="padding: 0 10px 0 10px;">
<table align="center" style="display: block;  text-align:center; width: 660px;border:2px solid;">
    <tbody style="display: block">
        <tr style="display: block">
            <td class="dark"  style="display: block; padding-bottom:8px; padding-top:5px; ">
                <h3 style="text-align: center; text-transform: uppercase;">
                    {{trans 'Thank You For Your Purchase'}}
                </h3>
            </td>
        </tr>
        <tr style="display: block">
            <td class="dark" align="center" style="display: block; padding-bottom:0px; ">
                <h1 style="text-align: center; margin: 0 !important">
                    {{trans 'We just received your order!'}}
                </h1>
            </td>
        </tr>
        <tr style="display: block">
            <td class="dark" align="center" style="display: block; padding-bottom:8px; ">
                <h3 style="text-align: center; letter-spacing: 0.025em;">
                    {{trans 'ORDER NUMBER: <span class="no-link">%increment_id</span>' increment_id=$order.increment_id |raw}}
                </h3>
            </td>
        </tr>
    </tbody>
</table>
<table align="center" style="padding-bottom:5px; padding-top:20px; width: 660px;margin-left:12px;">
    <tbody>
        <tr>
            <td align="left" style="padding-top: 10px;padding-bottom:10px;">
                <p class="greeting">{{trans "Hello %customer_name," customer_name=$order_data.customer_name}}</p>
            </td>
        </tr>
        <tr>
            <td>
                <p>
                    {{trans 'Thanks. Your order is in process. You will receive the invoice email, and once the order is shipped, we will send you an email with your order tracking number.' }}
                </p>

                <p style="margin: 10px 0 !important; margin-left:20px;">
                    {{trans 'If you would like to view the status of your order  <a href="%account_url" style="color: blue !important;">your account</a>'. account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}
                </p>

                <p>
                    <span style= "color:#d91f26 !important;">Congratulations! </span>{{trans 'You have earned'}} <span style= "color:#d91f26 !important;">"LIX Rewards"</span>{{trans ' on this purchase. You can redeem this rewards on your next purchase, or whenever you feel like it.'}} 
                </p>
            </td>
        </tr>
    </tbody>
</table>

<table align="center" style="padding-bottom:5px; padding-top:20px; width: 660px;">
    <tr class="email-information">
        <td>
            <table class="order-details" style="border-top: 5px solid #000000">
                <tr>
                    <td class="address-details" style="padding-top: 20px !important; padding-left:20px;">
                        <h3 style="color: #555656;">{{trans "BILLING ADDRESS"}}</h3>
                        <p style="color: #555656;">{{var formattedBillingAddress|raw}}</p>
                    </td>
                    {{depend order_data.is_not_virtual}}
                    <td class="address-details" style="padding-top: 20px !important">
                        <h3 style="color: #555656;">{{trans "SHIPPING ADDRESS"}}</h3>
                        <p style="color: #555656;">{{var formattedShippingAddress|raw}}</p>
                    </td>
                    {{/depend}}
                </tr>
                <tr>
                    <td class="method-info wp-method-info" style="padding-bottom:20px !important;padding-left:20px;">
                        <h3 style="color: #555656;">{{trans "PAYMENT METHOD"}}</h3>
                        {{var payment_html|raw}}
                    </td>
                    {{depend order_data.is_not_virtual}}
                    <td class="method-info" style="padding-bottom: 20px !important">
                        <h3 style="color: #555656;">{{trans "SHIPPING METHOD"}}</h3>
                        <p style="color: #555656;">{{var order.shipping_description}}</p>
                        {{if shipping_msg}}
                        <p style="color: #555656;">{{var shipping_msg}}</p>
                        {{/if}}
                    </td>
                    {{/depend}}
                </tr>
            </table>
            
            {{depend order_data.email_customer_note}}
            <table class="message-info">
                <tr>
                    <td>
                        {{var order_data.email_customer_note|escape|nl2br}}
                    </td>
                </tr>
            </table>
            {{/depend}}

            {{layout handle="weltpixel_sales_email_order_items" order=$order order_id=$order_id area="frontend"}}
        </td>
    </tr>
    
    <tr>
        <td colspan="2" align="center">
            <table style="display: block" class="button" width="100%" border="0" cellspacing="0" cellpadding="0">
                <tbody style="display: block">
                    <tr style="display: block">
                        <td style="display: block">
                            <table class="inner-wrapper" border="0" cellspacing="0" cellpadding="0" align="center" width="100%">
                                <tr>
                                    <td align="center" style="padding: 8px 0 !important">
                                        <a href="{{var baseUrl}}profile/orders/{{var order.increment_id}}" target="_blank" style="font-weight: bold">{{trans "VIEW ORDER"}}</a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </td>
    </tr>
    
    <tr>
        <td style="margin-left: 0px">
            <p style="margin: 10px 0 !important;padding-left:20px;">
                {{trans 'If you have questions about your order, please contact us at <a href="mailto:%store_email" style="color: blue !important;">%store_email</a>' store_email=$store_email |raw}}
            </p>
        </td>
    </tr>
    
    <tr>
        <td style="margin-left: 0px">
            <p style="margin: 10px 0 !important; padding-left:20px;">
                {{trans 'Thanks.'}}
            </p>
        </td>
    </tr>
</table>

<p style="padding-left:28px; padding-top: 10px;">{{trans "Regards,"}} V3</p>
<p style="padding-left:28px;color:#d91f26 !important;font-weight: bold !important;">{{trans "ComAve"}}</p>

</div>

{{template config_path="design/email/footer_template"}}
